import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;

import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/dashboard_statistics_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Dashboard overview widget with KPI cards and recent activity
class DashboardOverviewWidget extends ConsumerWidget {
  const DashboardOverviewWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final dashboardState = ref.watch(dashboardStatisticsProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final isTablet = AdminResponsiveLayout.isTablet(context);
    final isRefreshing = ValueNotifier<bool>(false);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.background.withOpacity(0.97),
            theme.colorScheme.background.withOpacity(0.92),
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with refresh button
              _buildHeader(context, theme, isRefreshing, ref),
              const SizedBox(height: 24),

              // Error message
              if (dashboardState.error != null) ...[
                _buildErrorCard(theme, dashboardState.error!, ref),
                const SizedBox(height: 16),
              ],

              // KPI Cards Grid
              _buildKPICardsGrid(
                context,
                ref,
                isDesktop,
                isTablet,
                dashboardState,
              ),
              const SizedBox(height: 32),

              // Bottom section with recent activity and system health
              _buildBottomSection(
                context,
                ref,
                isDesktop,
                isTablet,
                dashboardState,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ShadThemeData theme,
    ValueNotifier<bool> isRefreshing,
    WidgetRef ref,
  ) {
    final isMobile = AdminResponsiveLayout.isMobile(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Platform Overview',
                style: theme.textTheme.h2.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF065F46), // emerald-800
                  fontSize: isMobile ? 20 : null,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                isMobile
                    ? 'Monitor key metrics in real-time.'
                    : 'Monitor key metrics and platform performance in real-time.',
                style: theme.textTheme.muted.copyWith(
                  fontSize: isMobile ? 12 : null,
                ),
                maxLines: isMobile ? 2 : 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        ValueListenableBuilder<bool>(
          valueListenable: isRefreshing,
          builder: (context, refreshing, _) {
            return ShadIconButton.ghost(
              icon: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child:
                    refreshing
                        ? const SizedBox(
                          key: ValueKey('refreshing'),
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Color(0xFF065F46),
                            ),
                          ),
                        )
                        : Icon(
                          lucide.LucideIcons.refreshCw,
                          key: const ValueKey('refresh'),
                          size: 20,
                        ),
              ),
              onPressed: () {
                if (!refreshing) {
                  isRefreshing.value = true;
                  ref.read(dashboardStatisticsProvider.notifier).refresh().then(
                    (_) {
                      Future.delayed(const Duration(milliseconds: 1000), () {
                        isRefreshing.value = false;
                      });
                    },
                  );
                }
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildErrorCard(ShadThemeData theme, String error, WidgetRef ref) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              lucide.LucideIcons.alertCircle,
              color: theme.colorScheme.destructive,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                error,
                style: TextStyle(color: theme.colorScheme.destructive),
              ),
            ),
            ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.x, size: 16),
              onPressed: () {
                ref.read(dashboardStatisticsProvider.notifier).clearError();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKPICardsGrid(
    BuildContext context,
    WidgetRef ref,
    bool isDesktop,
    bool isTablet,
    DashboardStatisticsState dashboardState,
  ) {
    final crossAxisCount = AdminResponsiveLayout.getCrossAxisCount(
      context,
      mobileCount: 2,
      tabletCount: 3,
      desktopCount: 4,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      mainAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      childAspectRatio: isDesktop ? 1.4 : 1.1,
      children: [
        _buildUserKPICard(ref, dashboardState),
        _buildClaimsKPICard(ref, dashboardState),
        _buildFundingKPICard(ref, dashboardState),
        _buildRegistrationsKPICard(ref, dashboardState),
      ],
    );
  }

  Widget _buildUserKPICard(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);

    return _EnhancedKPICard(
      title: 'Total Users',
      value: notifier.getKPIValue(DashboardKPI.totalUsers),
      subtitle: notifier.getKPISubtitle(DashboardKPI.totalUsers),
      icon: lucide.LucideIcons.users,
      accentColor: Colors.blue,
      trend: notifier.getKPITrend(DashboardKPI.totalUsers),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref
            .read(adminDashboardProvider.notifier)
            .setSelectedNavItem(AdminNavigationItem.userManagement);
      },
    );
  }

  Widget _buildClaimsKPICard(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);

    return _EnhancedKPICard(
      title: 'Active Claims',
      value: notifier.getKPIValue(DashboardKPI.activeClaims),
      subtitle: notifier.getKPISubtitle(DashboardKPI.activeClaims),
      icon: lucide.LucideIcons.fileText,
      accentColor: Colors.green,
      trend: notifier.getKPITrend(DashboardKPI.activeClaims),
      isLoading: dashboardState.isLoading,
      onTap: () {
        // TODO: Navigate to claims management when implemented
      },
    );
  }

  Widget _buildFundingKPICard(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);

    return _EnhancedKPICard(
      title: 'Funding Volume',
      value: notifier.getKPIValue(DashboardKPI.fundingVolume),
      subtitle: notifier.getKPISubtitle(DashboardKPI.fundingVolume),
      icon: lucide.LucideIcons.trendingUp,
      accentColor: Colors.purple,
      trend: notifier.getKPITrend(DashboardKPI.fundingVolume),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref
            .read(adminDashboardProvider.notifier)
            .setSelectedNavItem(AdminNavigationItem.analytics);
      },
    );
  }

  Widget _buildRegistrationsKPICard(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);

    return _EnhancedKPICard(
      title: 'New Registrations',
      value: notifier.getKPIValue(DashboardKPI.newRegistrations),
      subtitle: notifier.getKPISubtitle(DashboardKPI.newRegistrations),
      icon: lucide.LucideIcons.userPlus,
      accentColor: Colors.amber,
      trend: notifier.getKPITrend(DashboardKPI.newRegistrations),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref
            .read(adminDashboardProvider.notifier)
            .setSelectedNavItem(AdminNavigationItem.userManagement);
      },
    );
  }

  Widget _buildBottomSection(
    BuildContext context,
    WidgetRef ref,
    bool isDesktop,
    bool isTablet,
    DashboardStatisticsState dashboardState,
  ) {
    if (isDesktop) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System Health (1/3 width)
          Expanded(
            flex: 1,
            child: _buildEnhancedSystemHealthSection(ref, dashboardState),
          ),
          const SizedBox(width: 24),
          // Recent Activity (2/3 width)
          Expanded(
            flex: 2,
            child: _buildEnhancedRecentActivitySection(ref, dashboardState),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          _buildEnhancedSystemHealthSection(ref, dashboardState),
          const SizedBox(height: 24),
          _buildEnhancedRecentActivitySection(ref, dashboardState),
        ],
      );
    }
  }

  Widget _buildEnhancedRecentActivitySection(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final recentActivities = ref.watch(recentActivitiesProvider);

    return _EnhancedRecentActivityCard(
      activities: recentActivities,
      isLoading: dashboardState.isLoading,
      onViewAll: () {
        ref
            .read(adminDashboardProvider.notifier)
            .setSelectedNavItem(AdminNavigationItem.auditLogs);
      },
    );
  }

  Widget _buildEnhancedSystemHealthSection(
    WidgetRef ref,
    DashboardStatisticsState dashboardState,
  ) {
    final systemHealth = ref.watch(systemHealthProvider);

    if (systemHealth == null) {
      return _EnhancedSystemHealthCard(
        systemHealth: SystemHealth(
          status: SystemHealthStatus.warning,
          description: 'Loading system health...',
          cpuUsage: 0.0,
          memoryUsage: 0.0,
          diskUsage: 0.0,
          activeConnections: 0,
          lastChecked: DateTime.now(),
        ),
        isLoading: dashboardState.isLoading,
        onTap: () {
          // TODO: Navigate to system health details when implemented
        },
      );
    }

    return _EnhancedSystemHealthCard(
      systemHealth: systemHealth,
      isLoading: dashboardState.isLoading,
      onTap: () {
        // TODO: Navigate to system health details when implemented
      },
    );
  }
}

class _EnhancedKPICard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color accentColor;
  final TrendIndicator? trend;
  final bool isLoading;
  final VoidCallback? onTap;

  const _EnhancedKPICard({
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.accentColor,
    this.trend,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colorScheme.border),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Accent color left border
            Container(
              width: 4,
              height: double.infinity,
              decoration: BoxDecoration(
                color: accentColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
            ),
            // Main content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          title,
                          style: theme.textTheme.muted.copyWith(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: accentColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(icon, size: 16, color: accentColor),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(12, 4, 12, 8),
                    child:
                        isLoading
                            ? const SizedBox(
                              height: 24,
                              child: Center(
                                child: LinearProgressIndicator(
                                  backgroundColor: Colors.transparent,
                                ),
                              ),
                            )
                            : Text(
                              value,
                              style: theme.textTheme.h4.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                  if (subtitle != null)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getBadgeColor(trend).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          subtitle!,
                          style: theme.textTheme.small.copyWith(
                            color: _getBadgeColor(trend),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  // Gradient line at the bottom
                  Container(
                    height: 2,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [accentColor.withOpacity(0.7), accentColor],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getBadgeColor(TrendIndicator? trend) {
    if (trend == null) return Colors.grey;
    switch (trend) {
      case TrendIndicator.up:
        return Colors.green;
      case TrendIndicator.down:
        return Colors.red;
      case TrendIndicator.stable:
        return Colors.grey;
    }
  }
}

class _EnhancedSystemHealthCard extends StatelessWidget {
  final SystemHealth systemHealth;
  final bool isLoading;
  final VoidCallback? onTap;

  const _EnhancedSystemHealthCard({
    required this.systemHealth,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'System Health',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      systemHealth.status,
                    ).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getStatusText(systemHealth.status),
                    style: TextStyle(
                      color: _getStatusColor(systemHealth.status),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(systemHealth.description, style: theme.textTheme.muted),
          ),
          if (isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: LinearProgressIndicator(),
            )
          else
            Column(
              children: [
                _buildProgressItem(
                  context,
                  'CPU',
                  systemHealth.cpuUsage,
                  Colors.blue,
                ),
                _buildProgressItem(
                  context,
                  'Memory',
                  systemHealth.memoryUsage,
                  Colors.purple,
                ),
                _buildProgressItem(
                  context,
                  'Disk',
                  systemHealth.diskUsage,
                  Colors.amber,
                ),
              ],
            ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ShadButton.outline(
              onPressed: onTap,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(lucide.LucideIcons.activity, size: 16),
                  const SizedBox(width: 8),
                  const Text('View Details'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    double value,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label, style: const TextStyle(fontSize: 12)),
              Text(
                '${value.toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: LinearProgressIndicator(
              value: value / 100,
              backgroundColor: color.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 6,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(SystemHealthStatus status) {
    switch (status) {
      case SystemHealthStatus.healthy:
        return Colors.green;
      case SystemHealthStatus.warning:
        return Colors.orange;
      case SystemHealthStatus.critical:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(SystemHealthStatus status) {
    switch (status) {
      case SystemHealthStatus.healthy:
        return 'Healthy';
      case SystemHealthStatus.warning:
        return 'Warning';
      case SystemHealthStatus.critical:
        return 'Critical';
      default:
        return 'Unknown';
    }
  }
}

class _EnhancedRecentActivityCard extends StatelessWidget {
  final List<RecentActivity>? activities;
  final bool isLoading;
  final VoidCallback? onViewAll;

  const _EnhancedRecentActivityCard({
    this.activities,
    this.isLoading = false,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Recent Activity',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
          if (isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: LinearProgressIndicator(),
            ),
          SizedBox(height: 300, child: _buildActivityList()),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ShadButton.outline(
              onPressed: onViewAll,
              child: const Center(child: Text('View All Activity')),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList() {
    if (activities == null || activities!.isEmpty) {
      return const Center(child: Text('No recent activity'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activities!.length,
      itemBuilder: (context, index) {
        final activity = activities![index];
        return _buildActivityItem(activity);
      },
    );
  }

  Widget _buildActivityItem(RecentActivity activity) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getActivityColor(activity.type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity.type),
              size: 16,
              color: _getActivityColor(activity.type),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTimestamp(activity.timestamp),
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  Color _getActivityColor(String type) {
    switch (type.toLowerCase()) {
      case 'user_registration':
      case 'login':
        return Colors.blue;
      case 'claim_submission':
      case 'form_submission':
        return Colors.green;
      case 'profile_update':
        return Colors.purple;
      case 'system_event':
      case 'security_event':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type.toLowerCase()) {
      case 'user_registration':
      case 'login':
        return lucide.LucideIcons.userPlus;
      case 'claim_submission':
      case 'form_submission':
        return lucide.LucideIcons.fileText;
      case 'profile_update':
        return lucide.LucideIcons.user;
      case 'system_event':
      case 'security_event':
        return lucide.LucideIcons.alertCircle;
      default:
        return lucide.LucideIcons.activity;
    }
  }
}
