import 'dart:convert';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_audit_models.dart';

/// Service for admin audit logging and retrieval
class AdminAuditService {
  static final AdminAuditService _instance = AdminAuditService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  AdminAuditService._internal();

  factory AdminAuditService() => _instance;

  /// Log an admin action with comprehensive details
  Future<void> logAdminAction({
    required String action,
    required String adminId,
    String? adminName,
    String? adminEmail,
    String? targetUserId,
    String? targetUserName,
    String? targetUserEmail,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? details,
    AdminActionSeverity severity = AdminActionSeverity.info,
    AdminActionType? actionType,
  }) async {
    try {
      // Determine action type if not provided
      final resolvedActionType = actionType ?? _getActionTypeFromAction(action);

      // Prepare audit entry data
      final auditEntry = {
        'admin_id': adminId,
        'action_type': resolvedActionType.value,
        'action': action,
        'severity': severity.value,
        'target_user_id': targetUserId,
        'entity_type': entityType,
        'entity_id': entityId,
        'details': {
          ...?details,
          'admin_name': adminName,
          'admin_email': adminEmail,
          'target_user_name': targetUserName,
          'target_user_email': targetUserEmail,
        },
        'ip_address': await _getCurrentIpAddress(),
        'user_agent': await _getUserAgent(),
        // Note: 'created' and 'updated' timestamps are automatically managed by PocketBase autodate fields
      };

      // Create audit log entry
      await _pocketBaseService.createRecord(
        collectionName: 'admin_audit_logs',
        data: auditEntry,
      );

      LoggerService.info('Admin action logged: $action by $adminName');

      // Trigger security alerts for critical actions
      if (severity == AdminActionSeverity.critical ||
          severity == AdminActionSeverity.security) {
        await _triggerSecurityAlert(auditEntry);
      }
    } catch (e) {
      LoggerService.error('Failed to log admin action: $action', e);
      // Don't rethrow - audit logging failure shouldn't break admin operations
    }
  }

  /// Log bulk operations with individual item tracking
  Future<void> logBulkOperation({
    required String operation,
    required List<String> targetIds,
    required String adminId,
    String? adminName,
    Map<String, dynamic>? details,
    AdminActionSeverity severity = AdminActionSeverity.info,
  }) async {
    try {
      final bulkDetails = {
        ...?details,
        'target_count': targetIds.length,
        'target_ids': targetIds,
        'operation_type': 'bulk',
      };

      await logAdminAction(
        action: 'bulk_$operation',
        adminId: adminId,
        adminName: adminName,
        details: bulkDetails,
        severity: severity,
        actionType: AdminActionType.bulkOperation,
      );

      LoggerService.info(
        'Bulk operation logged: $operation for ${targetIds.length} items',
      );
    } catch (e) {
      LoggerService.error('Failed to log bulk operation: $operation', e);
    }
  }

  /// Get audit logs with filtering and pagination
  Future<List<AdminAuditEntry>> getAuditLogs({
    String? adminId,
    String? action,
    AdminActionType? actionType,
    AdminActionSeverity? severity,
    String? targetUserId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    int page = 1,
    int perPage = 50,
    String sortBy = '-created',
  }) async {
    try {
      final filter = _buildAuditFilter(
        adminId: adminId,
        action: action,
        actionType: actionType,
        severity: severity,
        targetUserId: targetUserId,
        startDate: startDate,
        endDate: endDate,
        searchQuery: searchQuery,
      );

      LoggerService.info('Fetching audit logs with filter: $filter');

      final records = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: sortBy,
      );

      final auditEntries =
          records.items
              .map((record) => AdminAuditEntry.fromRecord(record))
              .toList();

      LoggerService.info('Retrieved ${auditEntries.length} audit log entries');
      return auditEntries;
    } catch (e) {
      LoggerService.error('Error fetching audit logs', e);
      rethrow;
    }
  }

  /// Get audit log count with filters
  Future<int> getAuditLogCount({
    String? adminId,
    String? action,
    AdminActionType? actionType,
    AdminActionSeverity? severity,
    String? targetUserId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) async {
    try {
      final filter = _buildAuditFilter(
        adminId: adminId,
        action: action,
        actionType: actionType,
        severity: severity,
        targetUserId: targetUserId,
        startDate: startDate,
        endDate: endDate,
        searchQuery: searchQuery,
      );

      final result = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        page: 1,
        perPage: 1,
        filter: filter,
      );

      return result.totalItems;
    } catch (e) {
      LoggerService.error('Error getting audit log count', e);
      return 0;
    }
  }

  /// Get audit statistics
  Future<AuditStatistics> getAuditStatistics() async {
    try {
      // Get total entries
      final totalResult = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        page: 1,
        perPage: 1,
      );
      final totalEntries = totalResult.totalItems;

      // Get today's entries
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final todayResult = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        filter: 'created >= "${todayStart.toIso8601String()}"',
        page: 1,
        perPage: 1,
      );
      final todayEntries = todayResult.totalItems;

      // Get security events
      final securityResult = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        filter: 'severity = "security"',
        page: 1,
        perPage: 1,
      );
      final securityEvents = securityResult.totalItems;

      // Get critical events
      final criticalResult = await _pocketBaseService.getList(
        collectionName: 'admin_audit_logs',
        filter: 'severity = "critical"',
        page: 1,
        perPage: 1,
      );
      final criticalEvents = criticalResult.totalItems;

      // TODO: Implement more detailed statistics aggregation
      // For now, return basic statistics
      return AuditStatistics(
        totalEntries: totalEntries,
        todayEntries: todayEntries,
        securityEvents: securityEvents,
        criticalEvents: criticalEvents,
        actionTypeCounts: {},
        severityCounts: {},
        topAdmins: [],
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error getting audit statistics', e);
      rethrow;
    }
  }

  /// Export audit logs in specified format
  Future<String> exportAuditLogs(AuditExportRequest request) async {
    try {
      final logs = await getAuditLogs(
        adminId: request.filter.adminId,
        action: request.filter.action,
        actionType: request.filter.actionType,
        severity: request.filter.severity,
        targetUserId: request.filter.targetUserId,
        startDate: request.filter.startDate,
        endDate: request.filter.endDate,
        searchQuery: request.filter.searchQuery,
        page: 1,
        perPage: 10000, // Export all matching records
      );

      switch (request.format) {
        case AuditExportFormat.csv:
          return _exportToCsv(logs, request.fields);
        case AuditExportFormat.json:
          return _exportToJson(logs, request.fields);
        case AuditExportFormat.pdf:
          return _exportToPdf(logs, request.fields);
      }
    } catch (e) {
      LoggerService.error('Error exporting audit logs', e);
      rethrow;
    }
  }

  /// Build filter query for PocketBase
  String _buildAuditFilter({
    String? adminId,
    String? action,
    AdminActionType? actionType,
    AdminActionSeverity? severity,
    String? targetUserId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) {
    List<String> filters = [];

    if (adminId != null && adminId.isNotEmpty) {
      filters.add('admin_id = "$adminId"');
    }

    if (action != null && action.isNotEmpty) {
      filters.add('action ~ "$action"');
    }

    if (actionType != null) {
      filters.add('action_type = "${actionType.value}"');
    }

    if (severity != null) {
      filters.add('severity = "${severity.value}"');
    }

    if (targetUserId != null && targetUserId.isNotEmpty) {
      filters.add('target_user_id = "$targetUserId"');
    }

    if (startDate != null) {
      filters.add('created >= "${startDate.toIso8601String()}"');
    }

    if (endDate != null) {
      filters.add('created <= "${endDate.toIso8601String()}"');
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      filters.add('''
        (action ~ "$searchQuery" || 
         details.admin_name ~ "$searchQuery" || 
         details.target_user_name ~ "$searchQuery")
      ''');
    }

    return filters.join(' && ');
  }

  /// Determine action type from action string
  AdminActionType _getActionTypeFromAction(String action) {
    final actionLower = action.toLowerCase();

    if (actionLower.contains('user') || actionLower.contains('profile')) {
      return AdminActionType.userManagement;
    } else if (actionLower.contains('content') ||
        actionLower.contains('blog') ||
        actionLower.contains('podcast')) {
      return AdminActionType.contentManagement;
    } else if (actionLower.contains('system') ||
        actionLower.contains('config')) {
      return AdminActionType.systemConfiguration;
    } else if (actionLower.contains('security') ||
        actionLower.contains('login') ||
        actionLower.contains('auth')) {
      return AdminActionType.securityEvent;
    } else if (actionLower.contains('export') ||
        actionLower.contains('download')) {
      return AdminActionType.dataExport;
    } else if (actionLower.contains('bulk')) {
      return AdminActionType.bulkOperation;
    } else if (actionLower.contains('notification') ||
        actionLower.contains('alert')) {
      return AdminActionType.notification;
    }

    return AdminActionType.userManagement; // Default
  }

  /// Get current IP address (placeholder)
  Future<String> _getCurrentIpAddress() async {
    // TODO: Implement actual IP address detection
    return 'unknown';
  }

  /// Get user agent (placeholder)
  Future<String> _getUserAgent() async {
    // TODO: Implement actual user agent detection
    return 'flutter_app';
  }

  /// Trigger security alert for critical actions
  Future<void> _triggerSecurityAlert(Map<String, dynamic> auditEntry) async {
    try {
      // TODO: Implement security alert system
      LoggerService.warning(
        'Security alert triggered for action: ${auditEntry['action']}',
      );
    } catch (e) {
      LoggerService.error('Failed to trigger security alert', e);
    }
  }

  /// Export to CSV format
  String _exportToCsv(List<AdminAuditEntry> logs, List<String> fields) {
    // TODO: Implement CSV export
    return 'CSV export not yet implemented';
  }

  /// Export to JSON format
  String _exportToJson(List<AdminAuditEntry> logs, List<String> fields) {
    final exportData = logs.map((log) => log.toJson()).toList();
    return jsonEncode(exportData);
  }

  /// Export to PDF format
  String _exportToPdf(List<AdminAuditEntry> logs, List<String> fields) {
    // TODO: Implement PDF export
    return 'PDF export not yet implemented';
  }
}
